import { isValidSignature, SIGNATURE_HEADER_NAME } from "@sanity/webhook";
import { NextRequest, NextResponse } from "next/server";
import * as z from "zod/mini";
import { sendNewsletters } from "@/app/[locale]/(site)/controllers/newsletter";

const secret = process.env.SANITY_WEBHOOK_SECRET;

const payloadSchema = z.object({
  _id: z.string(),
  _type: z.string(),
  operation: z.enum(["create", "update", "delete"]),
});

export async function POST(req: NextRequest) {
  try {
    const signature = req.headers.get(SIGNATURE_HEADER_NAME);

    if (!signature) {
      console.log("❌ No signature found in request headers");
      return NextResponse.json(
        { success: false, message: "No signature found" },
        { status: 401 },
      );
    }

    if (!secret) {
      console.log("❌ SANITY_WEBHOOK_SECRET environment variable not set");
      return NextResponse.json(
        { success: false, message: "Webhook secret not configured" },
        { status: 500 },
      );
    }

    const body = await req.text();

    const isValid = await isValidSignature(body, signature, secret);

    if (!isValid) {
      console.log("❌ Invalid webhook signature");
      return NextResponse.json(
        { success: false, message: "Invalid signature" },
        { status: 401 },
      );
    }

    const schema = payloadSchema.safeParse({
      ...JSON.parse(body),
      operation: req.headers.get("sanity-operation"),
    });

    if (!schema.success) {
      console.log("❌ Invalid webhook payload");
      return NextResponse.json(
        { success: false, message: "Invalid payload" },
        { status: 400 },
      );
    }

    const payload = schema.data;

    if (payload._type === "newsletter" && payload.operation === "create") {
      console.log("✅ Newsletter created:", payload._id);
      try {
        const result = await sendNewsletters(payload._id);

        if (result.success) {
          console.log(
            `📧 Newsletter sent successfully to ${result.sentCount} subscribers`,
          );
        } else {
          console.error("❌ Failed to send newsletter:", result.error);
        }
      } catch (error) {
        console.error("❌ Error sending newsletter:", error);
        // Don't fail the webhook if newsletter sending fails
        // The newsletter is still created, just not sent
      }
    }

    return NextResponse.json({
      success: true,
      message: "Webhook processed successfully",
    });
  } catch (error) {
    console.error("❌ Error processing Sanity webhook:", error);
    return NextResponse.json(
      { success: false, message: "Internal server error" },
      { status: 500 },
    );
  }
}

// Handle other HTTP methods
export async function GET() {
  return NextResponse.json(
    { message: "Sanity webhook endpoint is active" },
    { status: 200 },
  );
}

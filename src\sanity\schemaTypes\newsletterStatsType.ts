import { defineType, defineField } from "sanity";

export const newsletterStatsType = defineType({
  name: "newsletterStats",
  title: "Estatísticas da Newsletter",
  type: "document",
  readOnly: true,
  fields: [
    defineField({
      name: "newsletter",
      title: "Newsletter",
      type: "reference",
      to: [{ type: "newsletter" }],
      description: "Newsletter associada a estas estatísticas",
      validation: (Rule) => Rule.required().error("A newsletter é obrigatória"),
    }),
    defineField({
      name: "totalSubscribers",
      title: "Total de Assinantes",
      type: "number",
      description: "Número total de assinantes no momento do envio",
      validation: (Rule) =>
        Rule.required()
          .min(0)
          .error("O total de assinantes deve ser um número positivo"),
    }),
    defineField({
      name: "successCount",
      title: "Envios Bem-sucedidos",
      type: "number",
      description: "Número de emails enviados com sucesso",
      validation: (Rule) =>
        Rule.required()
          .min(0)
          .error("O número de sucessos deve ser um número positivo"),
    }),
    defineField({
      name: "failedCount",
      title: "Envios Falhados",
      type: "number",
      description: "Número de emails que falharam no envio",
      validation: (Rule) =>
        Rule.required()
          .min(0)
          .error("O número de falhas deve ser um número positivo"),
    }),
    defineField({
      name: "failedEmails",
      title: "Emails Falhados",
      type: "array",
      of: [{ type: "string" }],
      description: "Lista dos emails que falharam no envio",
      validation: (Rule) =>
        Rule.unique().error("Emails duplicados não são permitidos"),
    }),
  ],
  preview: {
    select: {
      newsletterSubject: "newsletter.subject",
      successCount: "successCount",
      totalSubscribers: "totalSubscribers",
      failedCount: "failedCount",
    },
    prepare(selection) {
      const { newsletterSubject, successCount, totalSubscribers, failedCount } =
        selection;
      const title = newsletterSubject || "Newsletter sem assunto";
      const successRate =
        totalSubscribers > 0
          ? Math.round((successCount / totalSubscribers) * 100)
          : 0;
      const failureRate =
        totalSubscribers > 0
          ? Math.round((failedCount / totalSubscribers) * 100)
          : 0;

      return {
        title: title,
        subtitle: `${successCount}/${totalSubscribers} enviados (${successRate}% sucesso, ${failureRate}% falha)`,
        // media: successRate >= 95 ? "✅" : successRate >= 90 ? "⚡" : "❌",
      };
    },
  },
  orderings: [
    {
      title: "Mais recente primeiro",
      name: "createdDesc",
      by: [{ field: "_createdAt", direction: "desc" }],
    },
    {
      title: "Por taxa de sucesso",
      name: "successRateDesc",
      by: [{ field: "successCount", direction: "desc" }],
    },
    {
      title: "Por newsletter",
      name: "newsletterAsc",
      by: [{ field: "newsletter.subject", direction: "asc" }],
    },
  ],
});

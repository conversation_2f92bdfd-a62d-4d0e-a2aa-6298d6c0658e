[{"of": [{"marks": {"annotations": [{"fields": [{"validation": [{"rules": [{"flag": "uri", "constraint": {"options": {"scheme": ["/^http$/", "/^https$/"], "allowRelative": false, "relativeOnly": false, "allowCredentials": false}}}], "level": "error"}], "name": "href", "type": "url", "title": "URL"}], "name": "link", "type": "object", "title": "URL"}], "decorators": [{"value": "strong", "title": "Strong"}, {"value": "em", "title": "Emphasis"}]}, "lists": [{"value": "bullet", "title": "Bullet"}], "styles": [{"value": "normal", "title": "Normal"}, {"value": "h1", "title": "H1"}, {"value": "h2", "title": "H2"}, {"value": "h3", "title": "H3"}, {"value": "h4", "title": "H4"}, {"value": "blockquote", "title": "Quote"}], "of": [], "type": "block"}, {"options": {"hotspot": true}, "fields": [{"name": "alt", "type": "string", "title": "Alternative Text"}], "type": "image"}], "name": "blockContent", "type": "array"}, {"fields": [{"validation": [{"rules": [{"flag": "presence", "constraint": "required"}, {"flag": "min", "constraint": 5}, {"flag": "max", "constraint": 150}], "level": "error", "message": "O assunto deve ter entre 5 e 150 caracteres"}], "description": "Linha de assunto do newsletter", "name": "subject", "type": "string", "title": "<PERSON><PERSON><PERSON>"}, {"validation": [{"rules": [{"flag": "presence", "constraint": "required"}], "level": "error", "message": "O conteúdo é obrigatório"}], "description": "Conteúdo principal do newsletter", "name": "body", "type": "blockContent", "title": "<PERSON><PERSON><PERSON><PERSON>"}, {"options": {"dateFormat": "DD/MM/YYYY", "timeFormat": "HH:mm"}, "description": "Data e hora em que o newsletter foi enviado", "readOnly": true, "hidden": "conditional", "name": "sentAt", "type": "datetime", "title": "Data de Envio"}, {"options": {"list": [{"title": "Pendente", "value": "pending"}, {"title": "Enviado", "value": "sent"}], "layout": "radio"}, "initialValue": "pending", "validation": [{"rules": [{"flag": "valid", "constraint": ["pending", "sent"]}, {"flag": "presence", "constraint": "required"}], "level": "error", "message": "O status é obrigatório"}], "description": "Estado atual do newsletter", "readOnly": true, "name": "status", "type": "string", "title": "Estado"}], "name": "newsletter", "type": "document"}, {"fields": [{"validation": [{"rules": [{"flag": "presence", "constraint": "required"}], "level": "error", "message": "A newsletter é obrigatória"}], "description": "Newsletter associada a estas estatísticas", "to": [{"type": "newsletter"}], "name": "newsletter", "type": "reference"}, {"validation": [{"rules": [{"flag": "presence", "constraint": "required"}, {"flag": "min", "constraint": 0}], "level": "error", "message": "O total de assinantes deve ser um número positivo"}], "description": "Número total de assinantes no momento do envio", "name": "totalSubscribers", "type": "number", "title": "Total de Assinantes"}, {"validation": [{"rules": [{"flag": "presence", "constraint": "required"}, {"flag": "min", "constraint": 0}], "level": "error", "message": "O número de sucessos deve ser um número positivo"}], "description": "Número de emails enviados com sucesso", "name": "successCount", "type": "number", "title": "<PERSON><PERSON><PERSON>"}, {"validation": [{"rules": [{"flag": "presence", "constraint": "required"}, {"flag": "min", "constraint": 0}], "level": "error", "message": "O número de falhas deve ser um número positivo"}], "description": "Número de emails que falharam no envio", "name": "failedCount", "type": "number", "title": "<PERSON><PERSON><PERSON>"}, {"description": "Lista dos emails que falharam no envio", "of": [{"type": "string"}], "name": "failedEmails", "type": "array", "title": "Emails Falhados"}], "readOnly": true, "name": "newsletterStats", "type": "document", "title": "Estatísticas da Newsletter"}, {"fields": [{"validation": [{"rules": [{"flag": "presence", "constraint": "required"}], "level": "error", "message": "Por favor, insira um endereço de e-mail válido"}], "description": "Endereço de e-mail do assinante", "readOnly": false, "name": "email", "type": "email", "title": "E-mail"}, {"options": {"list": [{"value": "active", "title": "Ativo"}, {"value": "unsubscribed", "title": "Cancelado"}], "layout": "radio"}, "initialValue": "active", "validation": [{"rules": [{"flag": "valid", "constraint": ["active", "unsubscribed"]}, {"flag": "presence", "constraint": "required"}], "level": "error", "message": "O status é obrigatório"}], "description": "Estado da assinatura", "readOnly": false, "name": "status", "type": "string", "title": "Estado"}, {"options": {"dateFormat": "DD/MM/YYYY", "timeFormat": "HH:mm"}, "description": "Data e hora em que o assinante se inscreveu", "readOnly": true, "name": "subscribedAt", "type": "datetime", "title": "Data de Assinatura"}, {"options": {"dateFormat": "DD/MM/YYYY", "timeFormat": "HH:mm"}, "description": "Data e hora em que o assinante cancelou a assinatura", "readOnly": true, "hidden": "conditional", "name": "unsubscribedAt", "type": "datetime", "title": "Data de Cancelamento"}, {"validation": [{"rules": [{"flag": "presence", "constraint": "required"}], "level": "error", "message": "O token é obrigatório"}], "description": "Token único para cancelamento da assinatura", "readOnly": true, "name": "unsubscribeToken", "type": "string", "title": "Token de Cancelamento"}], "name": "subscriber", "type": "document", "title": "<PERSON><PERSON><PERSON>"}]